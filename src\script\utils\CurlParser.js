/**
 * 工具curl导入解析方法（包含入参curl形式和出参JSON形式）
 */

// Curl命令解析器
class CurlParser {
    /**
     * 解析curl命令，提取请求信息
     * @param {string} curlCommand - curl命令字符串
     * @returns {Object} 包含url、method、headers、data、queryParams的对象
     */
    static parse(curlCommand) {
        if (!curlCommand || typeof curlCommand !== 'string') {
            return { url: '', method: 'GET', headers: {}, data: '', queryParams: {} };
        }

        const cleanCommand = curlCommand.replace(/\\\s*\n\s*/g, ' ').replace(/\s+/g, ' ').trim();
        const urlResult = this._extractUrl(cleanCommand);
        const method = this._extractMethod(cleanCommand);
        const headers = this._extractHeaders(cleanCommand);
        const data = this._extractData(cleanCommand);

        return { url: urlResult.url, method, headers, data, queryParams: urlResult.queryParams };
    }

    /**
     * 从curl命令中提取URL和查询参数
     * @param {string} command - 预处理后的curl命令
     * @returns {Object} 包含url和queryParams的对象
     * @private
     */
    static _extractUrl(command) {
        const result = { url: '', queryParams: {} };

        // 查找URL的优先级：--url参数 > curl后直接跟的URL > 第一个非请求头中的HTTP URL
        let urlString = this._findUrlString(command);

        if (urlString) {
            try {
                const urlObj = new URL(urlString);
                result.url = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
                urlObj.searchParams.forEach((value, key) => {
                    result.queryParams[key] = value;
                });
            } catch (e) {
                result.url = urlString;
            }
        }

        return result;
    }

    /**
     * 按优先级查找URL字符串
     * @param {string} command - curl命令
     * @returns {string} 找到的URL字符串
     * @private
     */
    static _findUrlString(command) {
        // 1. 查找 --url 参数
        const urlParamMatch = command.match(/--url\s+(['"]?)([^'"\s]+)\1/);
        if (urlParamMatch) return urlParamMatch[2];

        // 2. 查找curl命令后直接跟的URL
        const curlDirectMatch = command.match(/curl\s+(['"]?)(https?:\/\/[^'"\s]+)\1/);
        if (curlDirectMatch) return curlDirectMatch[2];

        // 3. 查找所有HTTP URL，选择第一个非请求头中的
        const httpMatches = command.match(/(https?:\/\/[^\s'"]+)/g);
        if (httpMatches && httpMatches.length > 0) {
            return this._selectNonHeaderUrl(command, httpMatches);
        }

        return '';
    }

    /**
     * 从HTTP URL列表中选择非请求头中的URL
     * @param {string} command - curl命令
     * @param {Array<string>} httpMatches - 匹配到的HTTP URL数组
     * @returns {string} 选中的URL字符串
     * @private
     */
    static _selectNonHeaderUrl(command, httpMatches) {
        for (const url of httpMatches) {
            const urlIndex = command.indexOf(url);
            const beforeUrl = command.substring(0, urlIndex);
            const isInHeader = /-H\s+['"][^'"]*$/.test(beforeUrl) || /(?:Origin|Referer|Host):\s*$/.test(beforeUrl);

            if (!isInHeader) {
                return url.replace(/^['"]|['"]$/g, '');
            }
        }
        return httpMatches[0].replace(/^['"]|['"]$/g, '');
    }

    /**
     * @private
     */
    static _extractMethod(command) {
        const methodMatch = command.match(/(?:--request\s+|--X\s+|-X\s+)(\w+)/i);
        if (methodMatch) {
            return methodMatch[1].toUpperCase();
        }
        return 'GET';
    }

    /**
     * @private
     */
    static _extractHeaders(command) {
        const headers = {};
        const headerPattern = /(?:--header\s+|-H\s+)(['"]?)([^'"]*?)\1(?:\s|$)/g;
        let match;

        while ((match = headerPattern.exec(command)) !== null) {
            const headerStr = match[2];
            const colonIndex = headerStr.indexOf(':');
            if (colonIndex > 0) {
                const key = headerStr.substring(0, colonIndex).trim();
                const value = headerStr.substring(colonIndex + 1).trim();
                if (key && value) {
                    headers[key] = value;
                }
            }
        }

        return headers;
    }

    /**
     * @private
     */
    static _extractData(command) {
        const dataPatterns = [/(?:--data\s+)/, /(?:--data-raw\s+)/, /(?:-d\s+)/];

        for (const pattern of dataPatterns) {
            const match = command.match(pattern);
            if (match) {
                const startIndex = match.index + match[0].length;
                const remainingCommand = command.substring(startIndex);
                return this._extractQuotedValue(remainingCommand);
            }
        }

        return '';
    }

    /**
     * @private
     */
    static _extractQuotedValue(str) {
        if (str.startsWith('\'') || str.startsWith('"')) {
            const quote = str[0];
            let endIndex = 1;
            let escapeNext = false;

            while (endIndex < str.length) {
                const char = str[endIndex];
                if (escapeNext) {
                    escapeNext = false;
                } else if (char === '\\') {
                    escapeNext = true;
                } else if (char === quote) {
                    return str.substring(1, endIndex);
                }
                endIndex++;
            }
            return str.substring(1);
        }

        const nextParamMatch = str.match(/^([^-\s][^\s]*)/);
        if (nextParamMatch) {
            return nextParamMatch[1];
        }
        return '';
    }
}

// 数据转换器类
class CurlToToolDataConverter {
    /**
     * 将curl命令和JSON响应转换为工具配置数据
     * @param {string} curlCommand - curl命令字符串
     * @param {string} jsonResponse - JSON响应数据字符串
     * @returns {Object} 包含basicInfoForm、apiInfoForm、paramHeader、params的工具配置对象
     */
    static convert(curlCommand, jsonResponse) {
        const curlData = CurlParser.parse(curlCommand);
        let responseData = {};

        try {
            responseData = JSON.parse(jsonResponse || '{}');
        } catch (e) {
            responseData = {};
        }

        const urlInfo = this._extractUrlInfo(curlData.url);
        const pathSegments = urlInfo.serviceUri.split('/').filter(Boolean);
        let toolNameEn;
        if (pathSegments.length > 0) {
            toolNameEn = pathSegments[pathSegments.length - 1];
        } else {
            toolNameEn = 'api_tool';
        }

        const paramHeader = Object.entries(curlData.headers).map((entry, index) => ({
            paramId: index + 1,
            paramName: entry[0],
            paramNameEn: entry[0].toLowerCase().replace(/[^a-z0-9]/g, '_'),
            paramDefaultValue: entry[1],
            paramDescription: `${entry[0]} 请求头`,
            isRequired: 0
        }));

        const inputParams = [];
        const outputParams = [];

        // 简化的JSON参数处理
        if (curlData.data) {
            const contentType = curlData.headers['Content-Type'] || curlData.headers['content-type'] || '';
            if (contentType.includes('application/json')) {
                try {
                    const requestData = JSON.parse(curlData.data);
                    this._addJsonParams(requestData, inputParams, 'IN', '$');
                } catch (e) {
                    console.log('JSON 解析失败:', curlData.data);
                }
            }
        }

        if (Object.keys(responseData).length > 0) {
            this._addJsonParams(responseData, outputParams, 'OUT', '$');
        }

        return {
            basicInfoForm: {
                toolName: `${toolNameEn}_工具`,
                toolNameEn: toolNameEn,
                toolSource: '',
                paramHandle: '',
                comment: '',
                toolDescription: ''
            },
            apiInfoForm: {
                serviceCode: '',
                serviceName: '',
                serviceProject: '',
                serviceModule: '',
                serviceUri: urlInfo.serviceUri,
                serviceIp: urlInfo.serviceIp,
                servicePort: urlInfo.servicePort,
                serviceMethod: curlData.method,
                isCmccFramework: 0,
                serviceComment: ''
            },
            paramHeader: paramHeader,
            params: [...inputParams, ...outputParams]
        };
    }

    /**
     * 从URL中提取服务信息
     * @param {string} url - URL字符串
     * @returns {Object} 包含serviceIp、servicePort、serviceUri的对象
     * @private
     */
    static _extractUrlInfo(url) {
        let serviceIp = '';
        let servicePort = '';
        let serviceUri = '';

        if (url) {
            try {
                const urlObj = new URL(url);
                serviceIp = urlObj.hostname;
                if (urlObj.port) {
                    servicePort = urlObj.port;
                } else if (urlObj.protocol === 'https:') {
                    servicePort = '443';
                } else {
                    servicePort = '80';
                }
                serviceUri = urlObj.pathname;
            } catch (e) {
                const urlParts = url.split('/');
                if (urlParts.length >= 3) {
                    const hostPart = urlParts[2];
                    const hostPortParts = hostPart.split(':');
                    serviceIp = hostPortParts[0] || '';
                    servicePort = hostPortParts[1] || '';
                    serviceUri = '/' + urlParts.slice(3).join('/');
                }
            }
        }

        return { serviceIp, servicePort, serviceUri };
    }

    /**
     * 递归解析JSON对象并添加参数
     * @param {Object} obj - 要解析的JSON对象
     * @param {Array} params - 参数数组（引用传递）
     * @param {string} paramInout - 参数方向（'IN'或'OUT'）
     * @param {string} path - JSON路径
     * @private
     */
    static _addJsonParams(obj, params, paramInout, path) {
        let paramId = params.length + 1;

        const traverse = (value, currentPath, isArrayElement = false) => {
            if (value === null || value === undefined) return;

            if (Array.isArray(value)) {
                // 添加数组本身作为参数
                params.push(this._createParam(paramId++, currentPath, paramInout, 'array', JSON.stringify(value), `${this._getParamName(currentPath)} 数组`));

                // 解析数组中的元素
                if (value.length > 0) {
                    // 取第一个元素作为数组元素的结构模板
                    const firstElement = value[0];
                    if (firstElement !== null && firstElement !== undefined) {
                        // 为数组元素生成通配符路径，使用 [*] 表示数组通配符
                        const arrayElementPath = `${currentPath}[*]`;
                        // 标记这是数组元素，不创建中间对象节点
                        traverse(firstElement, arrayElementPath, true);
                    }
                }
            } else if (typeof value === 'object') {
                // 如果不是数组元素的对象，才添加对象本身作为参数
                if (!isArrayElement && currentPath !== '$') {
                    params.push(this._createParam(paramId++, currentPath, paramInout, 'object', JSON.stringify(value), `${this._getParamName(currentPath)} 对象`));
                }

                // 递归解析对象的属性
                Object.keys(value).forEach(key => {
                    let newPath;
                    if (currentPath === '$') {
                        newPath = `$.${key}`;
                    } else {
                        newPath = `${currentPath}.${key}`;
                    }
                    traverse(value[key], newPath, false);
                });
            } else {
                // 基本类型参数
                params.push(this._createParam(paramId++, currentPath, paramInout, this._getParamType(value), String(value), `${this._getParamName(currentPath)} 参数`));
            }
        };

        traverse(obj, path, false);
    }

    /**
     * 创建参数对象
     * @param {number} paramId - 参数ID
     * @param {string} path - JSON路径
     * @param {string} paramInout - 参数方向
     * @param {string} paramType - 参数类型
     * @param {string} defaultValue - 默认值
     * @param {string} description - 参数描述
     * @returns {Object} 参数对象
     * @private
     */
    static _createParam(paramId, path, paramInout, paramType, defaultValue, description) {
        return {
            paramId: paramId,
            paramName: this._getParamName(path),
            paramNameEn: this._getParamNameEn(path),
            paramInout: paramInout,
            paramType: paramType,
            paramDefaultValue: defaultValue,
            paramDescription: description,
            paramJsonPath: path,
            isRequired: 0,
            isLeafNode: 1,
            isValid: 1,
            paramHandleChoice: 0,
            paramInType: 3
        };
    }

    /**
     * @private
     */
    static _getParamName(path) {
        const segments = path.split('.');
        const lastSegment = segments[segments.length - 1];
        // 处理数组索引和通配符，保留字段名但移除索引和通配符
        const cleanSegment = lastSegment.replace(/\[.*?\]/g, '');
        // 如果是根路径，返回 'root'
        if (cleanSegment === '$' || cleanSegment === '') {
            return 'root';
        }
        return cleanSegment;
    }

    /**
     * @private
     */
    static _getParamNameEn(path) {
        const name = this._getParamName(path);
        return name.replace(/[^a-zA-Z0-9]/g, '_');
    }

    /**
     * @private
     */
    static _getParamType(value) {
        if (typeof value === 'boolean') return 'boolean';
        if (typeof value === 'number') {
            if (Number.isInteger(value)) {
                return 'int';
            }
            return 'double';
        }
        if (typeof value === 'string') return 'string';
        if (Array.isArray(value)) return 'array';
        if (typeof value === 'object' && value !== null) return 'object';
        return 'string';
    }
}

/**
 * 将curl命令和JSON响应转换为工具配置数据（导出函数）
 * @param {string} curlCommand - curl命令字符串
 * @param {string} jsonResponse - JSON响应数据字符串
 * @returns {Object} 工具配置数据对象
 */
export function convertCurlToToolData(curlCommand, jsonResponse) {
    return CurlToToolDataConverter.convert(curlCommand, jsonResponse);
}
